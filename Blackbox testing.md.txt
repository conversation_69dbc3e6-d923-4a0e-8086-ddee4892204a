### Pengujian Blackbox

Berikut adalah rencana pengujian blackbox berdasarkan kebutuhan fungsional dan non-fungsional yang telah diidentifikasi.

#### 1. Pengujian Fungsional

| Test Case ID | Fitur | Deskripsi Pengujian | Langkah-langkah | Hasil yang Diharapkan |
| :--- | :--- | :--- | :--- | :--- |
| F-01 | Rekaman Suara | Memastikan pengguna dapat merekam suara untuk frasa yang diberikan. | 1. <PERSON><PERSON> halaman latihan.<br>2. <PERSON><PERSON> tombol "Mulai Merekam".<br>3. Ucapkan frasa yang ditampilkan.<br>4. <PERSON><PERSON> tombol "Berhenti Merekam". | Rekaman suara berhasil dibuat dan siap untuk dikirim. |
| F-02 | Integrasi API | Memastikan sistem dapat mengirim rekaman suara ke API model AI. | 1. <PERSON><PERSON>h menyelesaikan rekaman (langkah F-01).<br>2. Klik tombol "Kirim" atau "Evaluasi". | Sistem berhasil mengirim data rekaman ke endpoint API yang benar dan menerima kode status sukses (misal: 200 OK). |
| F-03 | Umpan Balik | Memastikan sistem menampilkan skor dan umpan balik dari AI. | 1. Setelah API memberikan respons (langkah F-02). | Halaman menampilkan skor pelafalan dan umpan balik (misal: kata yang salah ucap) dengan jelas. |
| F-04 | Manajemen Konten | Memastikan admin dapat mengelola daftar kata/kalimat. | 1. Login sebagai admin.<br>2. Buka halaman manajemen kata.<br>3. Tambah, ubah, atau hapus sebuah kata/kalimat. | Perubahan (tambah, ubah, hapus) berhasil disimpan di database dan terlihat di halaman manajemen. |
| F-05 | Pelacakan Progres | Memastikan sistem melacak progres latihan pengguna. | 1. Selesaikan satu sesi latihan.<br>2. Buka halaman "Progres Saya" atau "Riwayat". | Hasil latihan terakhir (skor, frasa, tanggal) tercatat dengan benar. |
| F-06 | Pemilihan Modul | Memastikan pengguna dapat memilih modul pelatihan. | 1. Buka halaman utama atau halaman "Pilih Modul".<br>2. Klik pada salah satu kategori fonologis (misal: "Plosives"). | Aplikasi menampilkan daftar latihan yang sesuai dengan kategori yang dipilih. |

<br>

#### 2. Pengujian Non-Fungsional

| Test Case ID | Kategori | Deskripsi Pengujian | Langkah-langkah | Hasil yang Diharapkan |
| :--- | :--- | :--- | :--- | :--- |
| NF-01 | Kinerja | Waktu respons untuk evaluasi pelafalan tidak lebih dari 30 detik. | 1. Lakukan pengujian F-02.<br>2. Ukur waktu dari menekan tombol "Kirim" hingga umpan balik muncul. | Waktu yang dibutuhkan secara konsisten di bawah 30 detik. |
| NF-02 | Kegunaan | Antarmuka mudah dipahami oleh pengguna pemula. | 1. Berikan aplikasi kepada pengguna baru tanpa instruksi.<br>2. Minta mereka untuk menyelesaikan satu sesi latihan. | Pengguna dapat menyelesaikan alur utama (rekam, kirim, lihat hasil) tanpa mengalami kebingungan atau kesalahan fatal. |
| NF-03 | Penyimpanan Data | Data pengguna disimpan secara lokal dengan aman. | 1. Selesaikan beberapa sesi latihan.<br>2. Tutup dan buka kembali aplikasi.<br>3. Periksa halaman progres.<br>4. (Opsional) Cari file database di perangkat. | Riwayat latihan tetap ada dan tidak hilang. Data tersimpan dalam format yang tidak mudah dibaca/diubah secara manual. |
| NF-04 | Kompatibilitas | Aplikasi web berjalan lancar di browser Chrome dan Firefox. | 1. Buka aplikasi di Chrome versi terbaru.<br>2. Lakukan semua test case fungsional.<br>3. Ulangi langkah 1 & 2 di Firefox versi terbaru. | Semua fungsi berjalan dengan baik dan tampilan tidak rusak di kedua browser. |
| NF-05 | Kemudahan Pakai | Sistem mudah dipakai oleh pengguna. | (Pengujian ini mirip dengan NF-02) Mengamati interaksi pengguna untuk menemukan titik kesulitan. | Pengguna dapat menavigasi antar halaman dan menggunakan fitur-fitur utama dengan lancar. |
| NF-06 | Operasi Offline | Aplikasi dapat berjalan sepenuhnya secara offline. | 1. Putuskan koneksi internet.<br>2. Buka aplikasi.<br>3. Lakukan seluruh alur latihan. | Aplikasi tetap berfungsi penuh, termasuk proses evaluasi (mengasumsikan model AI berjalan lokal atau di-cache). |

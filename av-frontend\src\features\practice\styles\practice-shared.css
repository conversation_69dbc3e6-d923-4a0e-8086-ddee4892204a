@import url('../../../styles/color-global.css');
/*
practice-shared.css
Styling tambahan untuk elemen-elemen umum di fitur practice (menyesuaikan fitur intro dan color-global)
*/

/*
practice-shared.css
Styling tambahan untuk elemen-elemen umum di fitur practice (menyesuaikan fitur intro)
*/

/* Container utama practice agar selalu di tengah dan full screen */
.practice-container {
  min-height: 100vh;
  min-width: 100vw;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: var(--color-bg-card-alt);
  box-sizing: border-box;
  overflow: auto;
  padding-bottom: 100px;
}

/* Container utama untuk responsif */
@media (max-width: 600px) {
  .practice-test-ctn,
  .practice-result-ctn {
    padding: 16px 4vw 16px 4vw;
    max-width: 98vw;
  }
  .practice-test-content,
  .practice-result-content {
    font-size: 0.98rem;
  }
  .practice-record-btn {
    width: 44px;
    height: 44px;
  }
  .practice-continue-btn, .practice-return-btn {
    padding: 10px 18px;
    font-size: 0.98rem;
  }
}

/* Utility: hidden */
.practice-hidden {
  display: none !important;
}

/* Utility: center text */
.practice-center {
  text-align: center !important;
}

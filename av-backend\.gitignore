# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# mypy
.mypy_cache/
.dmypy.json

# Pyre type checker
.pyre/

# VS Code
.vscode/

# Temporary files
*.tmp
*.log
*.bak
*.swp
*.swo
*.orig

# Audio files
*.wav
*.mp3
*.flac

# Model files
*.pt
*.pth
*.ckpt
*.h5
*.pb
*.onnx

# Model checkpoints and weights
checkpoints/
models/
pretrained/
pretrained_models/
# Wav2Vec2 checkpoints
wav2vec2_checkpoints/

@import url('../../../styles/color-global.css');
/* AureaVoice Category Page Base Styles */

/* App Layout */
#app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--color-bg-main);
  color: var(--color-text-main);
  font-family: 'Poppins', sans-serif;
}

/* Navbar Container */
.navbar-container {
  position: sticky;
  top: 0;
  z-index: 1000;
  width: 100%;
}

/* Category Container */
.category-container {
  flex: 1;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  overflow-x: hidden;
  position: relative;
}

/* Content Wrapper */
.category-content {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
}

/* Banner Section */
.category-banner {
  background: var(--color-bg-banner);
  color: var(--color-text-main);
  padding: 3rem 1rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--color-border-light);
}

.banner-content {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
  align-items: center;
}

.banner-text {
  z-index: 2;
}

.banner-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--color-text-main);
}

.banner-subtitle {
  font-size: 1.125rem;
  color: var(--color-text-secondary);
  margin: 0;
  line-height: 1.6;
}

.banner-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.banner-img {
  width: 100%;
  max-width: 400px;
  height: 200px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: var(--color-banner-img-shadow);
}

/* Main Layout */
.category-layout {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem 2rem;
  display: grid;
  grid-template-columns: 5fr 2fr; /* ini bagian penting */
  gap: 2rem;
  grid-template-areas: "main sidebar";
}


.category-main {
  grid-area: main;
}

.category-sidebar {
  grid-area: sidebar;
}

/* Section Titles */
.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-slate-800);
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--color-slate-200);
}

/* Material Article Section */
.material-section {
  margin-bottom: 3rem;
}

.material-article {
  background: var(--color-bg-card);
  border: 1px solid var(--color-border-card);
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: var(--color-shadow);
  max-width: none;
}

.material-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--color-slate-200);
}

.material-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--color-text-main);
  margin-bottom: 1rem;
  line-height: 1.3;
}

.material-meta {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.reading-time {
  background: var(--color-bg-card-alt);
  color: var(--color-text-secondary);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid var(--color-border);
}

.material-content {
  color: #374151;
  line-height: 1.8;
  font-size: 1rem;
}

/* Article Typography */
.material-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-slate-800);
  margin: 2rem 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--color-slate-200);
}

.material-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-slate-800);
  margin: 1.5rem 0 0.75rem 0;
}

.material-content h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 1.25rem 0 0.5rem 0;
}

.material-content p {
  margin-bottom: 1rem;
  text-align: justify;
}

.material-content ul,
.material-content ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.material-content li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.material-content strong {
  font-weight: 600;
  color: var(--color-text-main);
}

.material-content em {
  font-style: italic;
  color: var(--color-text-accent);
  font-weight: 500;
}

.material-content code {
  background: var(--color-bg-card-alt);
  color: var(--color-text-accent);
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  border: 1px solid var(--color-border);
}

/* Pronunciation Section */
.pronunciation-section {
  margin-bottom: 3rem;
}

.pronunciation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.pronunciation-card {
  background: var(--color-bg-card);
  border: 1px solid var(--color-border-card);
  border-radius: 0.75rem;
  padding: 1.25rem;
  transition: all 0.3s ease;
  box-shadow: var(--color-shadow);
}

.pronunciation-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--color-shadow-hover);
  border-color: var(--color-primary);
}

.pronunciation-word {
  margin-bottom: 1rem;
}

.word-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text-main);
  margin-bottom: 0.25rem;
}

.phonetic-text {
  color: var(--color-text-accent);
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.pronunciation-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.play-button {
  background: var(--color-primary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.play-button:hover {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
}

.play-icon {
  font-size: 0.75rem;
}

/* Difficulty Badges */
.difficulty-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.difficulty-easy {
  background: var(--color-bg-badge-easy);
  color: var(--color-text-easy);
}

.difficulty-medium {
  background: var(--color-bg-badge-medium);
  color: var(--color-text-medium);
}

.difficulty-hard {
  background: var(--color-bg-badge-hard);
  color: var(--color-text-hard);
}

/* Common Mistakes Section */
.mistakes-section {
  margin: 2rem 0;
  padding: 0;
}

.mistakes-section .section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-slate-800);
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--color-slate-200);
}

.mistakes-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.25rem;
  margin: 0;
  padding: 0;
  list-style: none;
}

.mistake-card {
  background: var(--color-bg-card);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px var(--color-shadow);
  transition: all 0.2s ease;
  border: 1px solid var(--color-slate-200);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.mistake-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--color-shadow);
  border-color: var(--color-border-light);
}

.mistake-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--color-text-main);
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.mistake-title::before {
  content: '⚠️';
  font-size: 1.25rem;
}

.mistake-description {
  color: var(--color-text-muted);
  font-size: 0.95rem;
  line-height: 1.7;
  margin-bottom: 1.25rem;
  padding-left: 0.25rem;
}

.mistake-examples {
  background: var(--color-bg-card-alt);
  border-radius: 6px;
  padding: 0.75rem;
  margin-top: 1rem;
  border: 1px solid var(--color-border);
  flex-grow: 1;
}

.mistake-examples strong {
  display: block;
  color: var(--color-text-muted);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.mistake-examples ul {
  margin: 0.5rem 0 0 0;
  padding-left: 1.25rem;
}

.mistake-examples li {
  color: var(--color-text-muted);
  font-size: 0.9rem;
  line-height: 1.7;
  margin-bottom: 0.5rem;
  position: relative;
  padding-left: 0.5rem;
}

.mistake-examples li::before {
  content: '•';
  color: var(--color-primary);
  font-weight: bold;
  display: inline-block;
  width: 1em;
  margin-left: -1em;
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .mistakes-grid {
    grid-template-columns: 1fr;
  }
}

/* More Materials Section */
.more-materials-section {
  margin-bottom: 3rem;
}

.more-materials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1rem;
}

.material-link-card {
  background: var(--color-bg-card);
  border: 1px solid var(--color-border-card);
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-decoration: none;
  color: inherit;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.2s ease;
  box-shadow: var(--color-shadow);
}

.material-link-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--color-shadow-hover);
  border-color: var(--color-primary);
  text-decoration: none;
  color: inherit;
}

.material-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.material-info {
  flex: 1;
}

.material-link-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-main);
  margin-bottom: 0.5rem;
}

.material-link-description {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 0.75rem;
}

.material-type-badge {
  background: var(--color-bg-card-alt);
  color: var(--color-text-badge);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid var(--color-border);
}

.external-link-icon {
  font-size: 1.25rem;
  color: var(--color-link-external);
  flex-shrink: 0;
}

/* Empty State */
.empty-state {
  text-align: center;
  color: var(--color-text-secondary);
  font-style: italic;
  padding: 2rem;
  background: var(--color-bg-card-alt);
  border-radius: 8px;
  border: 1px dashed var(--color-border-light);
}

@import url('../../../styles/color-global.css');
/* Processing state: yellow background, spinner handled in JS */
.intro-microphone-btn.intro-processing {
  background: var(--color-secondary);
  box-shadow: 0 0 0 4px var(--color-secondary-light);
  color: var(--color-white);
  opacity: 0.7;
  pointer-events: none;
  position: relative;
}

/* Spinner for processing state */
.intro-mic-spinner {
  width: 28px;
  height: 28px;
  border: 3px solid var(--color-white);
  border-top: 3px solid var(--color-secondary-mid);
  border-radius: 50%;
  animation: intro-mic-spin 0.8s linear infinite;
  margin: 0 auto;
  display: block;
}

@keyframes intro-mic-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
/* Microphone Button Styles */
.intro-microphone-btn {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 32px;
  transition: box-shadow 0.2s, transform 0.2s;
  box-shadow: var(--color-shadow);
  border-radius: 50%;
  width: 56px;
  height: 56px;
  padding: 0;
  background: var(--color-bg-card);
}

.intro-microphone-btn:hover {
  box-shadow: var(--color-shadow-hover);
  transform: translateY(-4px) scale(1.07);
}

.intro-round-shadow {
  border-radius: 50%;
  background: var(--color-bg-card);
  box-shadow: var(--color-shadow);
  width: 56px;
  height: 56px;
  padding: 0;
}

.intro-microphone-icon {
  width: 32px;
  height: 32px;
  color: var(--color-primary);
  transition: color 0.2s;
}

/* Recording Duration Display */
.intro-recording-duration {
  position: relative;
  background: var(--color-slate-800);
  color: var(--color-white);
  margin-top: 20px;
  padding: 6px 18px;
  border-radius: 16px;
  font-family: 'Inter', monospace;
  font-size: 18px;
  font-weight: bold;
  z-index: 2;
  backdrop-filter: blur(10px);
  border: 1px solid var(--color-border-light);
  width: max-content;
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s cubic-bezier(.4,0,.2,1), transform 0.3s cubic-bezier(.4,0,.2,1);
  pointer-events: auto;
  display: block;
}

.intro-recording-duration.hide {
  opacity: 0;
  transform: translateY(10px);
  pointer-events: none;
}

.intro-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
}

/* Try Again Button Styles */
.intro-try-again-button {
  padding: 12px 32px;
  border-radius: 8px;
  background-color: var(--color-primary);
  border: none;
  cursor: pointer;
  color: var(--color-white);
  font-size: 1.1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: var(--color-shadow-hover);
  opacity: 1;
}
.intro-try-again-button:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px var(--color-primary-shadow-strong);
}
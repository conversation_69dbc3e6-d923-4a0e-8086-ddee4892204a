
@import url('../../../styles/color-global.css');
/*
practice-test.css
Styling PracticeTestView mengikuti style fitur intro dan color-global
*/

.practice-test-ctn {
  width: 100%;
  max-width: 800px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 50px;
}

.practice-test-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.practice-top-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.practice-text {
  font-size: 1.25rem;
  font-weight: 500;
  color: var(--color-text-main);
  margin-bottom: 8px;
  text-align: center;
}


.practice-record-duration {
  position: relative;
  background: var(--color-slate-800);
  color: var(--color-white);
  margin-top: 20px;
  padding: 6px 18px;
  border-radius: 16px;
  font-family: 'Inter', monospace;
  font-size: 18px;
  font-weight: bold;
  z-index: 2;
  backdrop-filter: blur(10px);
  border: 1px solid var(--color-border-light);
  width: max-content;
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
  display: block;
  transition:
    opacity 0.45s cubic-bezier(.4,0,.2,1),
    transform 0.45s cubic-bezier(.4,0,.2,1),
    filter 0.45s cubic-bezier(.4,0,.2,1);
  filter: blur(0px);
}

.practice-record-duration.hide {
  opacity: 0;
  transform: translateY(12px) scale(0.95);
  pointer-events: none;
}

/* Smooth transition for wrapper when duration appears/disappears */
.practice-record-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  transition: margin-top 0.45s cubic-bezier(.4,0,.2,1), transform 0.45s cubic-bezier(.4,0,.2,1);
}
.practice-record-wrapper.shifted {
  transform: translateY(20px) scale(0.98);
}

.practice-record-btn {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: box-shadow 0.2s, transform 0.2s;
  box-shadow: var(--color-shadow);
  border-radius: 50%;
  width: 56px;
  height: 56px;
  padding: 0;
  background: var(--color-bg-card);
  position: relative;
}

.practice-record-btn:hover {
  box-shadow: var(--color-shadow-hover);
  transform: translateY(-4px) scale(1.07);
}

.practice-record-btn.practice-record-btn-active {
  background: var(--color-red);
  box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.15);
  color: var(--color-white);
  animation: practice-breathing 1.4s infinite ease-in-out;
}
.practice-record-btn.practice-record-btn-active .microphone-icon {
  color: var(--color-white);
}

@keyframes practice-breathing {
  0% {
    box-shadow: 0 0 0 4px rgba(245, 101, 101, 0.15), 0 4px 16px rgba(66, 153, 225, 0.2);
    transform: scale(0.9);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(245, 101, 101, 0.10), 0 4px 24px rgba(66, 153, 225, 0.25);
    transform: scale(1);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(245, 101, 101, 0.15), 0 4px 16px rgba(66, 153, 225, 0.2);
    transform: scale(0.9);
  }
}

.practice-record-btn.practice-record-btn-processing {
  background: var(--color-secondary);
  box-shadow: 0 0 0 4px var(--color-secondary-light);
  color: var(--color-white);
  opacity: 0.7;
  pointer-events: none;
  position: relative;
}

.practice-spinner {
  width: 28px;
  height: 28px;
  border: 3px solid var(--color-white);
  border-top: 3px solid var(--color-secondary-mid);
  border-radius: 50%;
  animation: practice-spin 0.8s linear infinite;
  margin: 0 auto;
  display: block;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

@keyframes practice-spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

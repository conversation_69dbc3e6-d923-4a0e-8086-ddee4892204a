# Panduan Pengisian Lampiran untuk Laporan Proyek AureaVoice

Dokumen ini bertujuan untuk memandu Anda dalam mengumpulkan dan menyusun materi untuk bagian lampiran dari laporan atau penulisan ilmiah terkait proyek AureaVoice. <PERSON><PERSON><PERSON> yang baik akan berisi bukti-bukti teknis yang mendukung metodologi dan hasil penelitian Anda.

---

## 1. Konfigurasi Deployment dan Lingkungan

Bagian ini menunjukkan bagaimana keseluruhan sistem diatur, di-build, dan dijalankan. Ini penting untuk menunjukkan reprodusibilitas dari penelitian Anda.

### 1.1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Docker Compose)
Sertakan isi dari file ini untuk menunjukkan bagaimana layanan backend dan frontend dihubungkan dan dikonfigurasi secara bersamaan.
- **Lokasi File:** `docker-compose.yml`

### 1.2. Konfigu<PERSON>i Container Backend
File ini mendefinisikan bagaimana environment untuk aplikasi backend (Python/FastAPI) dibuat.
- **Lokasi File:** `av-backend/Dockerfile`

### 1.3. Konfigurasi Container Frontend
File ini mendefinisikan bagaimana environment untuk aplikasi frontend (JavaScript/Node.js) dibuat.
- **Lokasi File:** `av-frontend/Dockerfile`

---

## 2. Daftar Dependensi (Libraries dan Framework)

Sertakan daftar lengkap dari semua pustaka (libraries) dan framework yang digunakan. Ini menunjukkan di atas teknologi apa aplikasi Anda dibangun.

### 2.1. Dependensi Backend (Python)
File ini berisi daftar semua paket Python yang dibutuhkan oleh backend.
- **Lokasi File:** `av-backend/requirements.txt`

### 2.2. Dependensi Frontend (Node.js)
Sertakan bagian `"dependencies"` dan `"devDependencies"` dari file ini. Ini berisi semua paket JavaScript yang dibutuhkan oleh frontend.
- **Lokasi File:** `av-frontend/package.json`

---

## 3. Potongan Kode Kunci (Key Code Snippets)

Daripada melampirkan seluruh kode sumber, pilih beberapa bagian paling penting yang merepresentasikan logika inti dari aplikasi Anda.

### 3.1. Backend: Endpoint API Deteksi Aksen
Cari dan salin fungsi di dalam `main.py` yang menangani request deteksi aksen. Biasanya ditandai dengan decorator seperti `@app.post("/predict")` atau serupa. Ini menunjukkan bagaimana data audio diterima dan diproses.
- **Lokasi File:** `av-backend/main.py`

### 3.2. Frontend: Service untuk Komunikasi ke API
Sertakan kode dari file ini yang bertanggung jawab untuk merekam audio dan mengirimkannya ke backend API. Cari fungsi yang menggunakan `fetch` atau `axios` untuk mengirim request ke server.
- **Lokasi File:** `av-frontend/src/utils/AccentDetectionService.js`
- **File Pendukung:** `av-frontend/src/utils/AudioRecorder.js`

---

## 4. Model Machine Learning yang Digunakan

Sebutkan model-model pre-trained yang menjadi inti dari fitur deteksi aksen.

- **Model Identifikasi Aksen:** Sebutkan nama model yang ada di dalam direktori `pretrained_models`, contohnya `accent-id-commonaccent_ecapa`.
- **Model Speech-to-Text/Feature Extractor:** Sebutkan model dasar yang digunakan, yang dapat diidentifikasi dari direktori checkpoints, yaitu `facebook/wav2vec2-large-xlsr-53`.

---

## 5. Dokumentasi Pengujian (Testing)

Jika Anda memiliki dokumentasi pengujian, ini adalah tempat yang tepat untuk melampirkannya.

### 5.1. Skenario Blackbox Testing
Anda sudah memiliki file ini. Salin isinya dan format dengan baik untuk menunjukkan bagaimana Anda memverifikasi fungsionalitas aplikasi dari sudut pandang pengguna.
- **Lokasi File:** `Blackbox testing.md.txt`

---

## Cara Menggunakan Panduan Ini:
1.  Buka setiap file yang disebutkan di atas.
2.  Salin kontennya.
3.  Tempelkan ke dalam dokumen lampiran Anda di bawah judul yang sesuai (misalnya, "Lampiran A: Konfigurasi Docker").
4.  Untuk potongan kode, berikan sedikit penjelasan mengenai apa yang dilakukan oleh kode tersebut.

@import url('../../../styles/color-global.css');
/*
practice-progress.css
Progress bar for session indicator in PracticeTestView, now using color-global
*/

.practice-session-progress-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 4px;
  background: var(--color-bg-card-alt);
  z-index: 1000;
  display: flex;
  box-shadow: var(--color-shadow);
}
.practice-session-progress-segment {
  flex: 1 1 0;
  height: 100%;
  background: var(--color-slate-200);
  transition: background 0.3s;
  border-right: 1px solid var(--color-white);
}
.practice-session-progress-segment:last-child {
  border-right: none;
}
.practice-session-progress-segment.active {
  background: var(--color-primary);
}
.practice-session-progress-segment.completed {
  background: var(--color-primary-dark);
}

@import url('../../../styles/color-global.css');
/* Category Navigation Styles - Specific to category pages only */
.category-container {
  /* Add padding to prevent content from being hidden behind fixed navbar */
  padding-top: 60px; /* Height of the navbar */
}

.category-nav {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--color-primary-gradient);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: 60px;
  display: flex;
  align-items: center;
}

/* Nav container */
.category-nav .nav-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

/* Logo styles */
.category-nav .nav-logo {
  font-size: 1.5rem;
  font-weight: 300;
  color: #ffffff;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.category-nav .nav-logo-aurea {
  color: #ffffff;
  font-weight: 600;
}

.category-nav .nav-logo-voice {
  color: var(--color-text-voice);
  font-weight: 400;
}

/* Navigation buttons */
.category-nav .nav-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.category-nav .nav-button {
  background: rgba(255, 255, 255, 0.15);
  color: #ffffff;
  padding: 0.5rem 1.25rem;
  border-radius: 0.5rem;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.category-nav .nav-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.category-nav .nav-button:active {
  transform: translateY(0);
}

/* Responsive styles */
@media (max-width: 768px) {
  .category-nav .nav-container {
    padding: 0 1.25rem;
  }
  
  .category-nav .nav-logo {
    font-size: 1.25rem;
  }
  
  .category-nav .nav-buttons {
    gap: 0.5rem;
  }
  
  .category-nav .nav-button {
    padding: 0.4rem 0.9rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .category-nav .nav-container {
    padding: 0 1rem;
  }
  
  .category-nav .nav-logo {
    font-size: 1.1rem;
  }
  
  .category-nav .nav-button {
    padding: 0.35rem 0.8rem;
    font-size: 0.8rem;
  }
}

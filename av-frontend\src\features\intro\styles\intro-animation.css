
@import url('../../../styles/color-global.css');
/* Microphone recording state styles (moved from intro-components.css) */
.intro-microphone-btn.intro-recording {
  background: var(--color-red);
  box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.15);
  color: var(--color-white);
  animation: intro-breathing 1.4s infinite ease-in-out;
}
.intro-microphone-btn.intro-recording .intro-microphone-icon {
  color: var(--color-white);
}

/* Breathing animation for microphone button */
@keyframes intro-breathing {
  0% {
    box-shadow: 0 0 0 4px rgba(245, 101, 101, 0.15), 0 4px 16px rgba(66, 153, 225, 0.2);
    transform: scale(0.9);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(245, 101, 101, 0.10), 0 4px 24px rgba(66, 153, 225, 0.25);
    transform: scale(1);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(245, 101, 101, 0.15), 0 4px 16px rgba(66, 153, 225, 0.2);
    transform: scale(0.9);
  }
}

.intro-microphone-btn.intro-recording {
  animation: intro-breathing 1.4s infinite ease-in-out;
}

/**
 * Application configuration
 */
class AppConfig {
  static get USE_REAL_MODEL() {
    // Selalu gunakan model asli saat di dalam Docker
    return true;
  }

  static get API_ENDPOINT() {
    // Gunakan environment variable jika ada, jika tidak, gunakan default untuk development
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000';
    return `${apiUrl}/classify-us-accent`;
  }

  // Demo/Mock mode settings (used when USE_REAL_MODEL = false)
  static get MOCK_DELAY_MIN() {
    return 1000;
  }

  static get MOCK_DELAY_MAX() {
    return 3000;
  }

  static get MOCK_CONFIDENCE_MIN() {
    return 60;
  }

  static get MOCK_CONFIDENCE_MAX() {
    return 95;
  }
}

export default AppConfig;

/* ===== TRANSITIONS CSS =====
 * File untuk mengatur segala jenis transisi dalam aplikasi
 */

/* ===== VIEW TRANSITION API SETUP ===== */
/* Enable View Transitions for SPA navigation */
@view-transition {
  navigation: auto;
}



/* ===== FALLBACK FOR UNSUPPORTED BROWSERS ===== */
/* Simple page transition for browsers without View Transition API */
@supports not (view-transition-name: none) {
  .intro-container {
    animation: simple-fade-in 0.3s ease-out forwards;
  }
}

@keyframes simple-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
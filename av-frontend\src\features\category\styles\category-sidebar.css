@import url('../../../styles/color-global.css');
/* AureaVoice Category Sidebar Styles */

/* Sidebar Container */
.category-sidebar {
  background: var(--color-bg-card);
  border-radius: 0.75rem;
  border: 1px solid var(--color-border-card);
  height: fit-content;
  position: sticky;
  top: 10px;
  box-shadow: var(--color-shadow);
}

.sidebar-content {
  padding: 1.5rem;
}

.sidebar-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text-main);
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--color-border);
}

/* Practice Items */
.practice-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.practice-item {
  background: var(--color-bg-card-alt);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.2s ease;
}

.practice-item:hover {
  background: var(--color-bg-main);
  border-color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: var(--color-shadow-hover);
}

.practice-header {
  margin-bottom: 0.5rem;
}

.practice-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--color-text-main);
  margin: 0;
  line-height: 1.4;
}

.practice-description {
  color: var(--color-text-secondary);
  font-size: 0.7rem;
  line-height: 1.5;
  margin-bottom: 0.6rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.practice-button {
  background: var(--color-primary);
  color: var(--color-white);
  border: none;
  padding: 0.5rem 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.7rem;
  font-weight: 600;
  width: 100%;
  transition: all 0.2s ease;
}

.practice-button:hover {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
}

.practice-button:active {
  transform: translateY(0);
}

/* Responsive Design for Sidebar */
@media (max-width: 1024px) {
  .category-layout {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "main"
      "sidebar";
    gap: 1.5rem;
  }
  
  .category-sidebar {
    position: static;
    top: auto;
  }
  
  .practice-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .sidebar-content {
    padding: 1rem;
  }
  
  .practice-items {
    grid-template-columns: 1fr;
  }
  

}
